import DriverNavigationBar from '@/components/DriverNavigationBar';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Trip data matching your description
const TRIP_DATA = {
  title: 'معلومات الرحلة',
  startTime: '7:00',
  endTime: '8:30',
  destination: 'جامعة العلوم',
  studentCount: 23,
  route: [
    { 
      latitude: 34.0209, 
      longitude: -6.8416,
      title: 'نقطة البداية',
      time: '7:00'
    },
    { 
      latitude: 34.0300, 
      longitude: -6.8200,
      title: 'جامعة العلوم',
      time: '8:30'
    },
  ],
};

export default function DriverTripTrackingScreen() {
  const router = useRouter();
  const [isStarting, setIsStarting] = useState(false);
  const [tripStarted, setTripStarted] = useState(false);

  const handleStartTrip = () => {
    setIsStarting(true);

    setTimeout(() => {
      setIsStarting(false);
      Alert.alert(
        'تم بدء الرحلة',
        'تم بدء الرحلة بنجاح. سيتم إشعار الطلاب بموقعك.',
        [
          {
            text: 'موافق',
            onPress: () => router.push('/driver-live-trip'),
          }
        ]
      );
    }, 2000);
  };

  const handleEndTrip = () => {
    Alert.alert(
      'إنهاء الرحلة',
      'هل أنت متأكد من إنهاء الرحلة؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'إنهاء',
          style: 'destructive',
          onPress: () => {
            setTripStarted(false);
            Alert.alert('تم إنهاء الرحلة', 'تم إنهاء الرحلة بنجاح.');
          }
        }
      ]
    );
  };

  const mapRegion = {
    latitude: 34.0255,
    longitude: -6.8300,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.light.primary} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>{TRIP_DATA.title}</Text>
          <Text style={styles.headerSubtitle}>Trip Information</Text>
        </View>
        <TouchableOpacity style={styles.menuButton}>
          <Text style={styles.menuIcon}>⋮</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Map Section */}
        <View style={styles.mapSection}>
          <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              initialRegion={mapRegion}
              showsUserLocation={true}
              showsMyLocationButton={false}
              showsCompass={false}
              toolbarEnabled={false}
            >
              {/* Route Polyline */}
              <Polyline
                coordinates={TRIP_DATA.route}
                strokeColor={Colors.light.primary}
                strokeWidth={4}
                lineDashPattern={[5, 5]}
              />
              
              {/* Route Markers */}
              {TRIP_DATA.route.map((location, index) => (
                <Marker
                  key={index}
                  coordinate={location}
                  pinColor={index === 0 ? Colors.light.success : Colors.light.error}
                  title={location.title}
                  description={`الوقت: ${location.time}`}
                />
              ))}
            </MapView>
            
            {/* Map Overlay with Times */}
            <View style={styles.mapOverlay}>
              <View style={styles.timeIndicator}>
                <Text style={styles.timeText}>{TRIP_DATA.startTime}</Text>
              </View>
              <View style={[styles.timeIndicator, styles.endTimeIndicator]}>
                <Text style={styles.timeText}>{TRIP_DATA.endTime}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Trip Details Section */}
        <View style={styles.detailsSection}>
          <View style={styles.tripInfoCard}>
            <Text style={styles.tripInfoTitle}>
              بداية الرحلة {TRIP_DATA.startTime} نهاية الرحلة {TRIP_DATA.endTime}
            </Text>
            
            <View style={styles.destinationContainer}>
              <Text style={styles.destinationIcon}>🎓</Text>
              <Text style={styles.destinationText}>{TRIP_DATA.destination}</Text>
            </View>
            
            <View style={styles.studentCountContainer}>
              <Text style={styles.studentIcon}>👥</Text>
              <Text style={styles.studentCountText}>{TRIP_DATA.studentCount} طالب</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons Section */}
        <View style={styles.actionSection}>
          {!tripStarted ? (
            <>
              <TouchableOpacity
                style={[styles.startButton, isStarting && styles.startButtonDisabled]}
                onPress={handleStartTrip}
                disabled={isStarting}
              >
                <Text style={styles.startButtonText}>
                  {isStarting ? 'جاري البدء...' : 'إبدأ الان'}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.unavailableButton} disabled>
                <Text style={styles.unavailableButtonText}>غير متاح</Text>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity
              style={styles.endButton}
              onPress={handleEndTrip}
            >
              <Text style={styles.endButtonText}>إنهاء الرحلة</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>

      {/* Navigation Bar */}
      <DriverNavigationBar currentScreen="trip-tracking" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 15,
    backgroundColor: Colors.light.primary,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  mapSection: {
    margin: 20,
    marginBottom: 10,
  },
  mapContainer: {
    height: 250,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    padding: 15,
  },
  timeIndicator: {
    backgroundColor: 'rgba(59, 130, 246, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  endTimeIndicator: {
    alignSelf: 'flex-end',
    backgroundColor: 'rgba(239, 68, 68, 0.9)',
  },
  timeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  detailsSection: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  tripInfoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  tripInfoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 20,
  },
  destinationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  destinationIcon: {
    fontSize: 24,
    marginRight: 10,
  },
  destinationText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  studentCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  studentIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  studentCountText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#6B7280',
  },
  actionSection: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  startButton: {
    backgroundColor: Colors.light.success,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: Colors.light.success,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonDisabled: {
    backgroundColor: '#9CA3AF',
    shadowOpacity: 0.1,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  unavailableButton: {
    backgroundColor: '#E5E7EB',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  unavailableButtonText: {
    color: '#9CA3AF',
    fontSize: 18,
    fontWeight: '600',
  },
  endButton: {
    backgroundColor: Colors.light.error,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: Colors.light.error,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  endButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
