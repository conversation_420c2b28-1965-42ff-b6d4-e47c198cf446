import GoogleMapsRoute from '@/components/GoogleMapsRoute';
import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

const { width, height } = Dimensions.get('window');

// Static locations for the trip
const TRIP_LOCATIONS = [
  {
    id: 1,
    name: 'منزل الطالب',
    type: 'home',
    latitude: 34.0209,
    longitude: -6.8416,
  },
  {
    id: 2,
    name: 'محطة الحافلة',
    type: 'bus_stop',
    latitude: 34.0250,
    longitude: -6.8350,
  },
  {
    id: 3,
    name: 'الجامعة',
    type: 'university',
    latitude: 34.0300,
    longitude: -6.8200,
  },
];

const TRIP_SCHEDULE = {
  leaveHome: '07:40',
  pickupTime: '07:50',
  arrivalTime: '08:30',
};



export default function TripDetailsScreen() {
  const router = useRouter();
  const [isStarting, setIsStarting] = useState(false);

  const handleGoPress = () => {
    setIsStarting(true);

    setTimeout(() => {
      setIsStarting(false);
      Alert.alert(
        'بدء الرحلة',
        'تم إشعار السائق وسيتم تتبع موقعك أثناء الرحلة.',
        [
          {
            text: 'موافق',
            onPress: () => router.push('/live-trip'),
          }
        ]
      );
    }, 2000);
  };

  const mapRegion = {
    latitude: 34.0255,
    longitude: -6.8300,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.light.gradientStart} />

      {/* Enhanced Header */}
      <View style={styles.enhancedHeader}>
        <TouchableOpacity
          style={styles.modernBackButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonIcon}>←</Text>
          <Text style={styles.backButtonText}>رجوع</Text>
        </TouchableOpacity>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>تفاصيل الرحلة</Text>
          <Text style={styles.headerSubtitle}>Trip Details</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerActionButton}>
            <Text style={styles.headerActionIcon}>📍</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Enhanced Route Section with Google Maps */}
        <View style={styles.routeSection}>
          <Text style={styles.sectionTitle}>مسار الرحلة</Text>
          <View style={styles.routeWrapper}>
            <GoogleMapsRoute
              locations={TRIP_LOCATIONS}
              style={styles.enhancedRoute}
              routeData={{
                duration: '40 min',
                distance: '12.5 km',
                eta: TRIP_SCHEDULE.arrivalTime,
              }}
            />
          </View>
        </View>

        {/* Enhanced Schedule Section */}
        <View style={styles.enhancedScheduleContainer}>
          <Text style={styles.sectionTitle}>جدول الرحلة</Text>

          <View style={styles.timelineContainer}>
            {/* Timeline Item 1 - Leave Home */}
            <View style={styles.timelineItem}>
              <View style={styles.timelineLeft}>
                <View style={[styles.timelineIcon, { backgroundColor: Colors.light.primary }]}>
                  <Text style={styles.iconText}>🏠</Text>
                </View>
                <View style={styles.timelineLine} />
              </View>
              <View style={styles.timelineContent}>
                <View style={styles.timelineHeader}>
                  <Text style={styles.timelineLabel}>وقت مغادرة المنزل</Text>
                  <View style={styles.timeChip}>
                    <Text style={styles.timeChipText}>{TRIP_SCHEDULE.leaveHome}</Text>
                  </View>
                </View>
                <Text style={styles.timelineDescription}>الوقت المقترح لمغادرة المنزل</Text>
                <View style={styles.statusBadge}>
                  <View style={[styles.statusDot, { backgroundColor: Colors.light.warning }]} />
                  <Text style={styles.statusText}>Upcoming</Text>
                </View>
              </View>
            </View>

            {/* Timeline Item 2 - Bus Pickup */}
            <View style={styles.timelineItem}>
              <View style={styles.timelineLeft}>
                <View style={[styles.timelineIcon, { backgroundColor: Colors.light.warning }]}>
                  <Text style={styles.iconText}>🚌</Text>
                </View>
                <View style={styles.timelineLine} />
              </View>
              <View style={styles.timelineContent}>
                <View style={styles.timelineHeader}>
                  <Text style={styles.timelineLabel}>وقت وصول الحافلة</Text>
                  <View style={styles.timeChip}>
                    <Text style={styles.timeChipText}>{TRIP_SCHEDULE.pickupTime}</Text>
                  </View>
                </View>
                <Text style={styles.timelineDescription}>الوقت المتوقع لوصول الحافلة للمحطة</Text>
                <View style={styles.statusBadge}>
                  <View style={[styles.statusDot, { backgroundColor: Colors.light.info }]} />
                  <Text style={styles.statusText}>Scheduled</Text>
                </View>
              </View>
            </View>

            {/* Timeline Item 3 - University Arrival */}
            <View style={[styles.timelineItem, styles.lastTimelineItem]}>
              <View style={styles.timelineLeft}>
                <View style={[styles.timelineIcon, { backgroundColor: Colors.light.secondary }]}>
                  <Text style={styles.iconText}>🎓</Text>
                </View>
              </View>
              <View style={styles.timelineContent}>
                <View style={styles.timelineHeader}>
                  <Text style={styles.timelineLabel}>وقت الوصول للجامعة</Text>
                  <View style={styles.timeChip}>
                    <Text style={styles.timeChipText}>{TRIP_SCHEDULE.arrivalTime}</Text>
                  </View>
                </View>
                <Text style={styles.timelineDescription}>الوقت المتوقع للوصول للجامعة</Text>
                <View style={styles.statusBadge}>
                  <View style={[styles.statusDot, { backgroundColor: Colors.light.secondary }]} />
                  <Text style={styles.statusText}>Destination</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Enhanced GO Button */}
        <View style={styles.goButtonContainer}>
          <TouchableOpacity
            style={[styles.enhancedGoButton, isStarting && styles.goButtonDisabled]}
            onPress={handleGoPress}
            disabled={isStarting}
            activeOpacity={0.8}
          >
            <View style={styles.goButtonGradient}>
              <View style={styles.goButtonContent}>
                {isStarting ? (
                  <View style={styles.loadingContainer}>
                    <View style={styles.loadingSpinner} />
                    <Text style={styles.goButtonText}>جاري البدء...</Text>
                  </View>
                ) : (
                  <>
                    <Text style={styles.goButtonText}>ابدأ</Text>
                    <Text style={styles.goButtonSubtext}>بدء الرحلة</Text>
                  </>
                )}
              </View>
            </View>
            <View style={styles.goButtonShadow} />
          </TouchableOpacity>
        </View>

        {/* Enhanced Info Section */}
        <View style={styles.enhancedInfoContainer}>
          <View style={styles.infoIcon}>
            <Text style={styles.infoIconText}>ℹ️</Text>
          </View>
          <Text style={styles.enhancedInfoText}>
            عند الضغط على "GO" سيتم إرسال إشعار للسائق وبدء تتبع موقعك
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  // Main Container
  container: {
    flex: 1,
    backgroundColor: Colors.light.gradientStart,
  },

  // Enhanced Header Styles
  enhancedHeader: {
    backgroundColor: Colors.light.gradientStart,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingBottom: 24,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  modernBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 16,
    backgroundColor: Colors.light.surfaceSecondary,
    borderWidth: 1,
    borderColor: Colors.light.borderLight,
  },
  backButtonIcon: {
    fontSize: 18,
    color: Colors.light.primary,
    fontWeight: '600',
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    fontWeight: '600',
  },
  headerTitleContainer: {
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.light.textTertiary,
    fontWeight: '500',
    marginTop: 2,
  },
  headerActions: {
    width: 40,
    alignItems: 'flex-end',
  },
  headerActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.surfaceSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActionIcon: {
    fontSize: 18,
    color: Colors.light.textSecondary,
    fontWeight: '600',
  },

  // Content Layout
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 20,
    textAlign: 'right',
  },
  // Enhanced Route Section
  routeSection: {
    marginBottom: 32,
  },
  routeWrapper: {
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 8,
  },
  enhancedRoute: {
    height: height * 0.35,
  },

  // Enhanced Route Component Styles
  enhancedRouteContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 24,
    overflow: 'hidden',
  },
  routeHeader: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  routeHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  routeInfo: {
    flex: 1,
  },
  routeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.background,
    marginBottom: 4,
  },
  routeSubtitle: {
    fontSize: 14,
    color: Colors.light.primaryLight,
    fontWeight: '500',
  },
  routeStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.background,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.primaryLight,
    fontWeight: '500',
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: Colors.light.primaryLight,
    marginHorizontal: 16,
  },
  // Route Visualization Styles
  routeVisualization: {
    height: 180,
    position: 'relative',
    backgroundColor: Colors.light.backgroundTertiary,
  },
  mapPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modernStreet: {
    position: 'absolute',
    height: 1,
    backgroundColor: Colors.light.border,
  },
  modernAvenue: {
    position: 'absolute',
    width: 1,
    backgroundColor: Colors.light.border,
  },
  animatedRoutePath: {
    position: 'absolute',
    left: '18%',
    top: '70%',
    height: 4,
    backgroundColor: Colors.light.primary,
    borderRadius: 2,
    transform: [{ rotate: '-25deg' }],
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  modernLocationMarker: {
    position: 'absolute',
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerPulse: {
    position: 'absolute',
    width: 24,
    height: 24,
    borderRadius: 12,
    opacity: 0.3,
  },
  markerCore: {
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.background,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },

  // Route Legend Styles
  routeLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.light.surfaceSecondary,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },

  // Traffic Status Styles
  trafficStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    backgroundColor: Colors.light.background,
  },
  trafficIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  trafficText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  // Enhanced Schedule Container
  enhancedScheduleContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 24,
    padding: 28,
    marginBottom: 32,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: Colors.light.borderLight,
  },

  // Timeline Styles
  timelineContainer: {
    paddingVertical: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 32,
  },
  lastTimelineItem: {
    marginBottom: 0,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 20,
  },
  timelineIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  iconText: {
    fontSize: 20,
  },
  timelineLine: {
    width: 2,
    height: 40,
    backgroundColor: Colors.light.border,
    marginTop: 8,
  },
  timelineContent: {
    flex: 1,
    paddingTop: 4,
  },
  timelineHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timelineLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    flex: 1,
    textAlign: 'right',
    marginRight: 12,
  },
  timeChip: {
    backgroundColor: Colors.light.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  timeChipText: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.background,
  },
  timelineDescription: {
    fontSize: 14,
    color: Colors.light.textTertiary,
    textAlign: 'right',
    marginBottom: 12,
    lineHeight: 20,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  // Enhanced GO Button Styles
  goButtonContainer: {
    alignItems: 'center',
    marginVertical: 32,
  },
  enhancedGoButton: {
    width: 140,
    height: 140,
    borderRadius: 70,
    position: 'relative',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  goButtonGradient: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  goButtonContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  goButtonText: {
    fontSize: 24,
    fontWeight: '800',
    color: Colors.light.textOnBlue,
    letterSpacing: 1,
  },
  goButtonSubtext: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.primaryShade,
    marginTop: 4,
    letterSpacing: 0.5,
  },
  goButtonShadow: {
    position: 'absolute',
    top: 4,
    left: 4,
    right: 4,
    bottom: 4,
    borderRadius: 66,
    backgroundColor: Colors.light.primary + '40',
    zIndex: -1,
  },
  goButtonDisabled: {
    shadowOpacity: 0.1,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingSpinner: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 3,
    borderColor: Colors.light.background + '30',
    borderTopColor: Colors.light.background,
    marginBottom: 8,
  },
  // Enhanced Info Container
  enhancedInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.info + '10',
    paddingVertical: 20,
    paddingHorizontal: 24,
    borderRadius: 20,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: Colors.light.info + '20',
  },
  infoIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.info + '20',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  infoIconText: {
    fontSize: 16,
  },
  enhancedInfoText: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.info,
    fontWeight: '600',
    lineHeight: 22,
    textAlign: 'right',
  },

});