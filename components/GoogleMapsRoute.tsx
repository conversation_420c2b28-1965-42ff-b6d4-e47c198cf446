import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, Platform, StyleSheet, Text, View } from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { Colors } from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

interface Location {
  id: number;
  name: string;
  type: 'home' | 'bus_stop' | 'university';
  latitude: number;
  longitude: number;
}

interface GoogleMapsRouteProps {
  locations: Location[];
  style?: any;
  routeData?: {
    duration: string;
    distance: string;
    eta: string;
  };
}

// Default route data
const DEFAULT_ROUTE_DATA = {
  duration: '40 min',
  distance: '12.5 km',
  eta: '08:30',
};

const GoogleMapsRoute: React.FC<GoogleMapsRouteProps> = ({ 
  locations, 
  style, 
  routeData = DEFAULT_ROUTE_DATA 
}) => {
  const mapRef = useRef<MapView>(null);
  const [mapReady, setMapReady] = useState(false);

  // Calculate map region to fit all locations
  const getMapRegion = () => {
    if (locations.length === 0) return null;

    const latitudes = locations.map(loc => loc.latitude);
    const longitudes = locations.map(loc => loc.longitude);

    const minLat = Math.min(...latitudes);
    const maxLat = Math.max(...latitudes);
    const minLng = Math.min(...longitudes);
    const maxLng = Math.max(...longitudes);

    const latDelta = (maxLat - minLat) * 1.5; // Add padding
    const lngDelta = (maxLng - minLng) * 1.5;

    return {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.max(latDelta, 0.01),
      longitudeDelta: Math.max(lngDelta, 0.01),
    };
  };

  // Get marker icon based on location type
  const getMarkerIcon = (type: string) => {
    switch (type) {
      case 'home':
        return '🏠';
      case 'bus_stop':
        return '🚌';
      case 'university':
        return '🎓';
      default:
        return '📍';
    }
  };

  // Get marker color based on location type
  const getMarkerColor = (type: string) => {
    switch (type) {
      case 'home':
        return Colors.light.success;
      case 'bus_stop':
        return Colors.light.warning;
      case 'university':
        return Colors.light.primary;
      default:
        return Colors.light.secondary;
    }
  };

  // Create polyline coordinates for the route
  const routeCoordinates = locations.map(location => ({
    latitude: location.latitude,
    longitude: location.longitude,
  }));

  const mapRegion = getMapRegion();

  useEffect(() => {
    if (mapReady && mapRef.current && locations.length > 0) {
      // Fit map to show all markers with padding
      mapRef.current.fitToCoordinates(routeCoordinates, {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  }, [mapReady, locations]);

  if (!mapRegion) {
    return (
      <View style={[style, styles.errorContainer]}>
        <Text style={styles.errorText}>No route data available</Text>
      </View>
    );
  }

  return (
    <View style={[style, styles.container]}>
      {/* Route Header */}
      <View style={styles.routeHeader}>
        <View style={styles.routeHeaderContent}>
          <View style={styles.routeInfo}>
            <Text style={styles.routeTitle}>Trip Route</Text>
            <Text style={styles.routeSubtitle}>Rabat, Morocco</Text>
          </View>
          <View style={styles.routeStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{routeData.duration}</Text>
              <Text style={styles.statLabel}>Duration</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{routeData.distance}</Text>
              <Text style={styles.statLabel}>Distance</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Google Maps */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={mapRegion}
        onMapReady={() => setMapReady(true)}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={false}
        showsScale={false}
        mapType="standard"
        customMapStyle={[
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }],
          },
        ]}
      >
        {/* Route Polyline */}
        <Polyline
          coordinates={routeCoordinates}
          strokeColor={Colors.light.primary}
          strokeWidth={4}
          lineDashPattern={[0]}
        />

        {/* Location Markers */}
        {locations.map((location, index) => (
          <Marker
            key={location.id}
            coordinate={{
              latitude: location.latitude,
              longitude: location.longitude,
            }}
            title={location.name}
            description={`Stop ${index + 1}`}
            pinColor={getMarkerColor(location.type)}
          />
        ))}
      </MapView>

      {/* Traffic Status */}
      <View style={styles.trafficStatus}>
        <View style={[styles.trafficIndicator, { backgroundColor: Colors.light.success }]} />
        <Text style={styles.trafficText}>Light Traffic • ETA {routeData.eta}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  routeHeader: {
    backgroundColor: Colors.light.gradientEnd,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },
  routeHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  routeInfo: {
    flex: 1,
  },
  routeTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 4,
  },
  routeSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  routeStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.primary,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.light.textMuted,
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: Colors.light.border,
    marginHorizontal: 16,
  },
  map: {
    height: 280,
    width: '100%',
  },
  trafficStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  trafficIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  trafficText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500',
  },
  errorContainer: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
  },
  errorText: {
    fontSize: 16,
    color: Colors.light.textMuted,
  },
});

export default GoogleMapsRoute;
